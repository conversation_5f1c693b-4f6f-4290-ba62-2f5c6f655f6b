# Use official Vite image as a parent image
# Choose a specific version for consistency and stability
FROM node:22-alpine

# Set environment variables
# Set NODE_ENV to development for development and production for production
ARG NODE_ENV=development
ENV NODE_ENV=${NODE_ENV}

# Create a directory for the app within the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json for dependency installation
COPY package*.json ./

# Install only production dependencies if NODE_ENV is production
RUN if [ "$NODE_ENV" = "production" ]; then \
      npm ci --only=production; \
    else \
      npm install; \
    fi

# Copy the rest of the application code
COPY . .

# Compile TypeScript files to JavaScript
RUN npm run build --if-present || true

# Expose the port the app runs on
# Replace 5183 with your application's port 
# Since we are using vite expose port 5183
EXPOSE 5183

# Command to run the application
# Use "npm start" or change as per your entry point in package.json
CMD ["npm","run", "dev"]
