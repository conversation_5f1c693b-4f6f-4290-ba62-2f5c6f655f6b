import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Clock, Play, Square,  Calendar } from "lucide-react";
import { toast } from "sonner";
import { useAuthHook } from "@/utils/useAuthHook";
import {
  useClockInMutation,
  useClockOutMutation,
  useLazyGetShiftsQuery,
} from "@/redux/slices/shifts";
import { useShiftManager } from "@/hooks/useShiftManager";

interface ClockInWidgetProps {
  className?: string;
}

const ClockInWidget: React.FC<ClockInWidgetProps> = ({ className = "" }) => {
  const { user_details } = useAuthHook();
  const [currentTime, setCurrentTime] = useState<string>("");
  const { shiftState, updateShift, clearShift } = useShiftManager();

  const [clockIn, { isLoading: isClockingIn }] = useClockInMutation();
  const [clockOut, { isLoading: isClockingOut }] = useClockOutMutation();
  const [getShifts] = useLazyGetShiftsQuery();

  // Update current time every second
  useEffect(() => {
    const updateCurrentTime = () => {
      const now = new Date();
      setCurrentTime(now.toLocaleTimeString());
    };

    updateCurrentTime();
    const timeInterval = setInterval(updateCurrentTime, 1000);

    return () => clearInterval(timeInterval);
  }, []);



  const handleClockIn = async () => {
    if (!user_details?.employee_no) {
      toast.error("Employee information not found");
      return;
    }

    try {
      const now = new Date();
      const today = now.toISOString().split("T")[0];

      console.log("Starting clock in process...");
      console.log("Looking for shifts for employee:", user_details.employee_no);
      console.log("Date filter:", today);

      // First, get the user's allocated shift for today
      // Use a broader search to find shifts for today
      const shiftsResponse = await getShifts({
        employee: user_details.employee_no,
        page_size: 10, // Get more results to find today's shift
        ordering: "-start_time",
      }).unwrap();

      console.log("Shifts response:", shiftsResponse);

      // Find shift for today from the results
      let currentShift = null;
      if (shiftsResponse.results && shiftsResponse.results.length > 0) {
        currentShift = shiftsResponse.results.find((shift: any) => {
          const shiftDate = shift.start_time.split("T")[0];
          console.log("Checking shift date:", shiftDate, "against today:", today);
          return shiftDate === today;
        });
      }

      console.log("Found shift for today:", currentShift);

      if (!currentShift) {
        toast.error("No shift allocated for today. Please contact your supervisor.");
        return;
      }

      // Determine if user is clocking in early or late based on allocated shift
      const shiftStartTime = new Date(currentShift.start_time);
      const actualStartTime = now;
      const timeDiffMinutes =
        (actualStartTime.getTime() - shiftStartTime.getTime()) / (1000 * 60);

      console.log("Shift start time:", shiftStartTime);
      console.log("Actual start time:", actualStartTime);
      console.log("Time difference (minutes):", timeDiffMinutes);

      const started_early = timeDiffMinutes < -5; // More than 5 minutes early
      const started_late = timeDiffMinutes > 5; // More than 5 minutes late

      console.log("Started early:", started_early);
      console.log("Started late:", started_late);

      // Generate unique code for shift entry (auto-generated by backend, but required in request)
      const entryCode = `SE${user_details.employee_no}${Date.now()
        .toString()
        .slice(-8)}`;

      // Calculate shift end time (use the shift's end_time or default to 8 hours)
      const shiftEndTime = currentShift.end_time ||
        new Date(new Date(currentShift.start_time).getTime() + 8 * 60 * 60 * 1000).toISOString();

      // Create shift entry (clock in) according to API specification
      const entryData = {
        code: entryCode, // Required by API even though auto-generated
        shift_start_time: currentShift.start_time,
        shift_end_time: shiftEndTime,
        start_time: now.toISOString(),
        // end_time is nullable for clock-in, so we don't include it
        started_early: started_early,
        started_late: started_late,
        ended_early: false,
        ended_late: false,
        shift: currentShift.code, // Reference to the shift
        workstation: currentShift.workstation || null, // Use shift's workstation if available
      };

      console.log("Entry data:", entryData);
      console.log("About to call clockIn API...");

      const shiftEntry = await clockIn(entryData).unwrap();
      console.log("Shift entry created:", shiftEntry);

      updateShift(currentShift, shiftEntry);

      // Show appropriate message based on timing
      if (started_early) {
        toast.success("Clocked in early! Have a great day!");
      } else if (started_late) {
        toast.warning("Clocked in late. Please try to arrive on time.");
      } else {
        toast.success("Successfully clocked in!");
      }
    } catch (error: any) {
      console.error("Clock in error:", error);
      console.error("Error details:", error?.data);
      toast.error(
        error?.data?.detail ||
          error?.data?.message ||
          error?.message ||
          "Failed to clock in"
      );
    }
  };

  const handleClockOut = async () => {
    if (!shiftState.currentShiftEntry?.code) {
      toast.error("No active shift entry found");
      return;
    }

    try {
      const now = new Date();

      // Determine if user is clocking out early or late
      const shiftEndTime = new Date(
        shiftState.currentShiftEntry.shift_end_time
      );
      const actualEndTime = now;
      const timeDiffMinutes =
        (shiftEndTime.getTime() - actualEndTime.getTime()) / (1000 * 60);

      const ended_early = timeDiffMinutes > 5; // More than 5 minutes early
      const ended_late = timeDiffMinutes < -5; // More than 5 minutes late

      const updateData = {
        end_time: now.toISOString(),
        ended_early,
        ended_late,
      };

      await clockOut({
        code: shiftState.currentShiftEntry.code,
        data: updateData,
      }).unwrap();

      clearShift();

      // Show appropriate message based on timing
      if (ended_early) {
        toast.info("Clocked out early. Have a great day!");
      } else if (ended_late) {
        toast.success("Clocked out. Thanks for staying late!");
      } else {
        toast.success("Successfully clocked out!");
      }
    } catch (error: any) {
      console.error("Clock out error:", error);
      toast.error(
        error?.data?.message || error?.message || "Failed to clock out"
      );
    }
  };

  const formatShiftStartTime = (startTime: string) => {
    if (!startTime) return "N/A";

    const date = new Date(startTime);
    if (isNaN(date.getTime())) return "N/A";

    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const isLoading = isClockingIn || isClockingOut;

  return (
    <Card
      className={`shadow-lg border-border/50 bg-gradient-to-br from-card to-card/95 ${className}`}
    >
      <CardContent className="p-3 sm:p-4">
        <div className="space-y-3 sm:space-y-4">
          {/* Header */}
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center">
              <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-xs sm:text-sm text-foreground truncate">
                Time Clock
              </h3>
              <p className="text-xs text-muted-foreground truncate">
                {user_details?.fullnames?.split(" ")[0] || "User"}
              </p>
            </div>
          </div>

          {/* Current Time */}
          <div className="text-center py-1 sm:py-2">
            <div className="text-lg sm:text-2xl font-bold text-foreground font-mono">
              {currentTime}
            </div>
            <div className="text-xs text-muted-foreground flex items-center justify-center gap-1">
              <Calendar className="h-3 w-3" />
              <span className="truncate">
                {new Date().toLocaleDateString()}
              </span>
            </div>
          </div>

          {/* Shift Status */}
          {shiftState.isActive && shiftState.currentShiftEntry ? (
            <div className="space-y-2 sm:space-y-3">
              <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-2 sm:p-3 rounded-lg border border-green-200 dark:border-green-800">
                <div className="flex items-center gap-2 mb-1 sm:mb-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs sm:text-sm font-medium text-green-700 dark:text-green-300">
                    Shift Active
                  </span>
                </div>
                <div className="text-xs text-green-600 dark:text-green-400 truncate">
                  Started:{" "}
                  {shiftState.currentShiftEntry?.start_time
                    ? formatShiftStartTime(shiftState.currentShiftEntry.start_time)
                    : "N/A"}
                </div>
              </div>

              {/* Elapsed Time */}
              <div className="text-center">
                <div className="text-base sm:text-lg font-bold text-primary font-mono">
                  {shiftState.elapsedTime}
                </div>
                <div className="text-xs text-muted-foreground">Time Worked</div>
              </div>

              {/* Clock Out Button */}
              <Button
                onClick={handleClockOut}
                disabled={isLoading}
                className="w-full h-10 sm:h-11 bg-gradient-to-r from-red-600 to-red-500 hover:from-red-500 hover:to-red-400 text-white font-medium transition-all duration-200 hover:scale-105 text-xs sm:text-sm"
              >
                <Square className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                {isClockingOut ? "Clocking Out..." : "Clock Out"}
              </Button>
            </div>
          ) : (
            <div className="space-y-2 sm:space-y-3">
              <div className="bg-gradient-to-r from-muted/50 to-muted/30 p-2 sm:p-3 rounded-lg border border-border/50">
                <div className="flex items-center gap-2 mb-1 sm:mb-2">
                  <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
                  <span className="text-xs sm:text-sm font-medium text-muted-foreground">
                    Not Clocked In
                  </span>
                </div>
                <div className="text-xs text-muted-foreground">
                  Ready to start your shift
                </div>
              </div>

              {/* Clock In Button */}
              <Button
                onClick={handleClockIn}
                disabled={isLoading}
                className="w-full h-10 sm:h-11 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white font-medium transition-all duration-200 hover:scale-105 text-xs sm:text-sm"
              >
                <Play className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                {isClockingIn ? "Clocking In..." : "Clock In"}
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ClockInWidget;
