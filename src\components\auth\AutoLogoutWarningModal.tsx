import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { AlertTriangle, Clock, Shield } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AutoLogoutWarningModalProps {
  isOpen: boolean;
  timeRemaining: number; // Time remaining in seconds
  onExtendSession: () => void;
  onLogoutNow?: () => void;
  className?: string;
}

const AutoLogoutWarningModal: React.FC<AutoLogoutWarningModalProps> = ({
  isOpen,
  timeRemaining,
  onExtendSession,
  onLogoutNow,
  className
}) => {
  // Format time remaining as MM:SS
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Calculate progress percentage (assuming 2 minutes total warning time)
  const totalWarningTime = 2 * 60; // 2 minutes in seconds
  const progressPercentage = Math.max(0, ((totalWarningTime - timeRemaining) / totalWarningTime) * 100);

  // Determine urgency level for styling
  const isUrgent = timeRemaining <= 30; // Last 30 seconds
  const isCritical = timeRemaining <= 10; // Last 10 seconds

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent
        className={cn(
          "sm:max-w-md",
          "border-2",
          isCritical ? "border-red-500 bg-red-50 dark:bg-red-950" :
          isUrgent ? "border-orange-500 bg-orange-50 dark:bg-orange-950" :
          "border-yellow-500 bg-yellow-50 dark:bg-yellow-950",
          "[&>button]:hidden", // Hide the close button
          className
        )}
      >
        <DialogHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900">
            {isCritical ? (
              <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400 animate-pulse" />
            ) : (
              <Clock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            )}
          </div>
          <DialogTitle className={cn(
            "text-lg font-semibold",
            isCritical ? "text-red-700 dark:text-red-300" :
            isUrgent ? "text-orange-700 dark:text-orange-300" :
            "text-yellow-700 dark:text-yellow-300"
          )}>
            Session Timeout Warning
          </DialogTitle>
          <DialogDescription className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              You will be automatically logged out due to inactivity.
            </p>
            <div className="space-y-3">
              <div className={cn(
                "text-2xl font-mono font-bold",
                isCritical ? "text-red-600 dark:text-red-400 animate-pulse" :
                isUrgent ? "text-orange-600 dark:text-orange-400" :
                "text-yellow-600 dark:text-yellow-400"
              )}>
                {formatTime(timeRemaining)}
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Time remaining</span>
                  <span>{Math.ceil(timeRemaining / 60)} min</span>
                </div>
                <Progress 
                  value={progressPercentage} 
                  className={cn(
                    "h-2",
                    isCritical ? "bg-red-200 dark:bg-red-800" :
                    isUrgent ? "bg-orange-200 dark:bg-orange-800" :
                    "bg-yellow-200 dark:bg-yellow-800"
                  )}
                />
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="flex-col space-y-2 sm:flex-col sm:space-x-0 sm:space-y-2">
          <Button
            onClick={onExtendSession}
            className={cn(
              "w-full",
              isCritical ? "bg-red-600 hover:bg-red-700 text-white" :
              isUrgent ? "bg-orange-600 hover:bg-orange-700 text-white" :
              "bg-yellow-600 hover:bg-yellow-700 text-white"
            )}
            size="lg"
          >
            <Shield className="mr-2 h-4 w-4" />
            Keep Me Logged In
          </Button>
          
          {onLogoutNow && (
            <Button
              onClick={onLogoutNow}
              variant="outline"
              className="w-full"
              size="sm"
            >
              Logout Now
            </Button>
          )}
        </DialogFooter>

        {/* Additional warning text */}
        <div className="mt-4 rounded-md bg-blue-50 dark:bg-blue-950 p-3">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-4 w-4 text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-xs text-blue-700 dark:text-blue-300">
                <strong>Security Notice:</strong> This helps protect your account from unauthorized access.
                Click "Keep Me Logged In" to continue your session.
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AutoLogoutWarningModal;
