import { apiSlice } from "../apiSlice";
import { Shift, ShiftEntry } from "../../types/pos";

// Request types for creating shifts
export interface CreateShiftRequest {
  code: string; // Required by API even though auto-generated
  employee: string;
  branch: number;
  revenue_center?: number;
  workstation?: number;
  start_time: string;
  end_time?: string;
  auto_ended?: boolean;
}

// Request types for shift entries (clock in/out)
export interface CreateShiftEntryRequest {
  code: string; // Required by API even though auto-generated
  shift: string; // Shift code
  workstation?: number;
  shift_start_time: string;
  shift_end_time: string;
  start_time: string;
  end_time?: string;
  started_early?: boolean;
  started_late?: boolean;
  ended_early?: boolean;
  ended_late?: boolean;
}

export interface UpdateShiftEntryRequest {
  code: string;
  end_time: string;
  ended_early?: boolean;
  ended_late?: boolean;
}

// Response types
export interface ShiftsListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Shift[];
}

export interface ShiftEntriesListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: ShiftEntry[];
}

export const shiftApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all shifts
    getShifts: builder.query<
      ShiftsListResponse,
      {
        code?: string;
        employee?: string;
        branch?: string;
        revenue_center?: string;
        workstation?: string;
        start_time?: string;
        end_time?: string;
        search?: string;
        ordering?: string;
        page?: number;
        page_size?: number;
      }
    >({
      query: (params) => ({
        url: "/shifts/",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Raw shifts API response:", response);

        // Handle the API response structure: {message: "...", data: {...}}
        if (response.data) {
          const transformed = {
            count: response.data.total_data || 0,
            next: response.data.links?.next || null,
            previous: response.data.links?.previous || null,
            results: response.data.results || []
          };
          console.log("Transformed shifts:", transformed);
          return transformed;
        }

        // Fallback for direct response
        if (response.results) {
          return response;
        }

        // Default fallback
        return {
          count: 0,
          next: null,
          previous: null,
          results: []
        };
      },
      providesTags: ["Shifts"],
    }),

    // Create a new shift
    createShift: builder.mutation<Shift, CreateShiftRequest>({
      query: (payload) => ({
        url: "/shifts/",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Shifts"],
    }),

    // Get shift by code
    getShift: builder.query<Shift, string>({
      query: (code) => ({
        url: `/shifts/${code}`,
        method: "GET",
      }),
      providesTags: (result, error, code) => [{ type: "Shifts", id: code }],
    }),

    // Update shift by code
    updateShift: builder.mutation<
      Shift,
      { code: string; data: Partial<CreateShiftRequest> }
    >({
      query: ({ code, data }) => ({
        url: `/shifts/${code}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { code }) => [
        { type: "Shifts", id: code },
        "Shifts",
      ],
    }),

    // Delete shift by code
    deleteShift: builder.mutation<void, string>({
      query: (code) => ({
        url: `/shifts/${code}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, code) => [
        { type: "Shifts", id: code },
        "Shifts",
      ],
    }),

    // Get all shift entries
    getShiftEntries: builder.query<ShiftEntriesListResponse, any>({
      query: (params) => ({
        url: "/shifts/shift-entries/",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Raw shift entries API response:", response);

        // Handle the API response structure: {message: "...", data: {...}}
        if (response.data) {
          const transformed = {
            count: response.data.total_data || 0,
            next: response.data.links?.next || null,
            previous: response.data.links?.previous || null,
            results: response.data.results || []
          };
          console.log("Transformed shift entries:", transformed);
          return transformed;
        }

        // Fallback for direct response
        if (response.results) {
          return response;
        }

        // Default fallback
        return {
          count: 0,
          next: null,
          previous: null,
          results: []
        };
      },
      providesTags: ["ShiftEntries"],
    }),

    // Create a new shift entry (clock in)
    clockIn: builder.mutation<ShiftEntry, CreateShiftEntryRequest>({
      query: (payload) => ({
        url: "/shifts/shift-entries/",
        method: "POST",
        body: payload,
      }),
      transformResponse: (response: any) => {
        console.log("Raw shift entry creation response:", response);

        // Handle the API response structure: {message: "...", data: {...}}
        if (response.data) {
          console.log("Transformed shift entry:", response.data);
          return response.data;
        }

        // Fallback for direct response
        console.log("Direct shift entry response:", response);
        return response;
      },
      invalidatesTags: ["ShiftEntries", "Shifts"],
    }),

    // Get shift entry by code
    getShiftEntry: builder.query<ShiftEntry, string>({
      query: (code) => ({
        url: `/shifts/shift-entries/${code}`,
        method: "GET",
      }),
      providesTags: (result, error, code) => [
        { type: "ShiftEntries", id: code },
      ],
    }),

    // Update shift entry (clock out)
    clockOut: builder.mutation<
      ShiftEntry,
      { code: string; data: Partial<UpdateShiftEntryRequest> }
    >({
      query: ({ code, data }) => ({
        url: `/shifts/shift-entries//${code}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { code }) => [
        { type: "ShiftEntries", id: code },
        "ShiftEntries",
        "Shifts",
      ],
    }),

    // Delete shift entry
    deleteShiftEntry: builder.mutation<void, string>({
      query: (code) => ({
        url: `/shifts/shift-entries/${code}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, code) => [
        { type: "ShiftEntries", id: code },
        "ShiftEntries",
      ],
    }),
  }),
});

export const {
  useGetShiftsQuery,
  useLazyGetShiftsQuery,
  useCreateShiftMutation,
  useGetShiftQuery,
  useLazyGetShiftQuery,
  useUpdateShiftMutation,
  useDeleteShiftMutation,
  useGetShiftEntriesQuery,
  useLazyGetShiftEntriesQuery,
  useClockInMutation,
  useGetShiftEntryQuery,
  useLazyGetShiftEntryQuery,
  useClockOutMutation,
  useDeleteShiftEntryMutation,
} = shiftApiSlice;
