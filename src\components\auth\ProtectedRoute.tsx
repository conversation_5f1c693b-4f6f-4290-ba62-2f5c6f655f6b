import { useAuthHook } from '@/utils/useAuthHook';
import { Navigate, Outlet, useLocation } from 'react-router-dom';

const ProtectedRoute = () => {
  const { isAuthenticated } = useAuthHook();
  const location = useLocation();

  if (!isAuthenticated) {
    return <Navigate to="/stations-auth" state={{ from: location.pathname }} replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;