import React from 'react';
import { useAutoLogout } from '@/hooks/useAutoLogout';
import AutoLogoutWarningModal from './AutoLogoutWarningModal';
import { useAuthHook } from '@/utils/useAuthHook';

interface AutoLogoutProviderProps {
  children: React.ReactNode;
  warningTime?: number; // Time in seconds before logout to show warning (default: 13 minutes)
  logoutTime?: number; // Time in seconds before auto logout (default: 15 minutes)
  enabled?: boolean; // Whether auto logout is enabled (default: true)
}

const AutoLogoutProvider: React.FC<AutoLogoutProviderProps> = ({
  children,
  warningTime = 13 * 60, // 13 minutes
  logoutTime = 15 * 60, // 15 minutes
  enabled = true
}) => {
  const { isAuthenticated } = useAuthHook();
  
  const { state, extendSession, forceLogout } = useAutoLogout({
    warningTime,
    logoutTime,
    enabled: enabled && isAuthenticated
  });

  return (
    <>
      {children}
      
      {/* Auto-logout warning modal */}
      <AutoLogoutWarningModal
        isOpen={state.isWarningVisible}
        timeRemaining={state.timeRemaining}
        onExtendSession={extendSession}
        onLogoutNow={forceLogout}
      />
    </>
  );
};

export default AutoLogoutProvider;
