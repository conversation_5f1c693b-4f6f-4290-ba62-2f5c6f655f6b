import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  CreditCard,
  User,
  Phone,
  Plus,
  Check,
  X,
  Eye,
  Download,
  Printer,
} from "lucide-react";
import { toast } from "sonner";

interface CardData {
  id?: string;
  cardNumber: string;
  holderName: string;
  phone: string;
  email: string;
  address: string;
  cardType: string;
  expiryDate: string;
  status: "active" | "inactive" | "pending";
  createdDate: string;
}

const CardCreation: React.FC = () => {
  const [formData, setFormData] = useState<Partial<CardData>>({
    cardNumber: "",
    holderName: "",
    phone: "",
    email: "",
    address: "",
    cardType: "",
    expiryDate: "",
  });
  
  const [isCreating, setIsCreating] = useState(false);
  const [selectedCard, setSelectedCard] = useState<CardData | null>(null);

  const mockCards: CardData[] = [
    {
      id: "1",
      cardNumber: "4532-1234-5678-9012",
      holderName: "John Doe",
      phone: "+1234567890",
      email: "<EMAIL>",
      address: "123 Main St, City, State",
      cardType: "Premium",
      expiryDate: "2026-12",
      status: "active",
      createdDate: "2024-01-15",
    },
    {
      id: "2",
      cardNumber: "4532-9876-5432-1098",
      holderName: "Jane Smith",
      phone: "+1987654321",
      email: "<EMAIL>",
      address: "456 Oak Ave, City, State",
      cardType: "Standard",
      expiryDate: "2025-08",
      status: "pending",
      createdDate: "2024-01-20",
    },
  ];

  const cardTypes = ["Standard", "Premium", "VIP", "Corporate"];

  const handleInputChange = (field: keyof CardData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const generateCardNumber = () => {
    const randomNum = () => Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const cardNumber = `4532-${randomNum()}-${randomNum()}-${randomNum()}`;
    setFormData(prev => ({ ...prev, cardNumber }));
  };

  const handleCreateCard = async () => {
    if (!formData.holderName || !formData.phone || !formData.cardType) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsCreating(true);
    
    setTimeout(() => {
      toast.success("Card created successfully!");
      setFormData({
        cardNumber: "",
        holderName: "",
        phone: "",
        email: "",
        address: "",
        cardType: "",
        expiryDate: "",
      });
      setIsCreating(false);
    }, 2000);
  };

  const getStatusColor = (status: CardData["status"]) => {
    switch (status) {
      case "active":
        return "bg-green-500/10 text-green-600 border-green-200";
      case "inactive":
        return "bg-red-500/10 text-red-600 border-red-200";
      case "pending":
        return "bg-orange-500/10 text-orange-600 border-orange-200";
      default:
        return "bg-gray-500/10 text-gray-600 border-gray-200";
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/stations/home">
            <Button className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:scale-105 transition-all duration-200">
              <ArrowLeft className="h-4 w-4" />
              Back to Menu
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Card Creation
            </h1>
            <p className="text-muted-foreground">
              Create and manage customer cards
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-6">
            <Card className="shadow-lg border-border/50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  <Plus className="h-5 w-5 text-primary" />
                  Create New Card
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="col-span-2">
                    <Label htmlFor="cardNumber">Card Number</Label>
                    <div className="flex gap-2">
                      <Input
                        id="cardNumber"
                        value={formData.cardNumber}
                        onChange={(e) => handleInputChange("cardNumber", e.target.value)}
                        placeholder="4532-1234-5678-9012"
                        className="border-border/50"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={generateCardNumber}
                        className="hover:scale-105 transition-all duration-200"
                      >
                        Generate
                      </Button>
                    </div>
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor="holderName">Card Holder Name *</Label>
                    <Input
                      id="holderName"
                      value={formData.holderName}
                      onChange={(e) => handleInputChange("holderName", e.target.value)}
                      placeholder="Enter full name"
                      className="border-border/50"
                    />
                  </div>

                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange("phone", e.target.value)}
                      placeholder="+1234567890"
                      className="border-border/50"
                    />
                  </div>

                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      placeholder="<EMAIL>"
                      className="border-border/50"
                    />
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor="address">Address</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange("address", e.target.value)}
                      placeholder="Enter full address"
                      className="border-border/50"
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="cardType">Card Type *</Label>
                    <Select
                      value={formData.cardType}
                      onValueChange={(value) => handleInputChange("cardType", value)}
                    >
                      <SelectTrigger className="border-border/50">
                        <SelectValue placeholder="Select card type" />
                      </SelectTrigger>
                      <SelectContent>
                        {cardTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="expiryDate">Expiry Date</Label>
                    <Input
                      id="expiryDate"
                      type="month"
                      value={formData.expiryDate}
                      onChange={(e) => handleInputChange("expiryDate", e.target.value)}
                      className="border-border/50"
                    />
                  </div>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={handleCreateCard}
                    disabled={isCreating}
                    className="flex-1 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-600 text-white hover:scale-105 transition-all duration-200"
                  >
                    {isCreating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Check className="h-4 w-4 mr-2" />
                        Create Card
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setFormData({
                      cardNumber: "",
                      holderName: "",
                      phone: "",
                      email: "",
                      address: "",
                      cardType: "",
                      expiryDate: "",
                    })}
                    className="hover:scale-105 transition-all duration-200"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Clear
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card className="shadow-lg border-border/50">
              <CardHeader>
                <CardTitle className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Recent Cards ({mockCards.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[600px]">
                  <div className="space-y-4">
                    {mockCards.map((card) => (
                      <Card
                        key={card.id}
                        className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] border-border/50 ${
                          selectedCard?.id === card.id
                            ? "ring-2 ring-primary/50 bg-primary/5"
                            : "hover:border-primary/50"
                        }`}
                        onClick={() => setSelectedCard(card)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <CreditCard className="h-5 w-5 text-primary" />
                              <span className="font-bold">{card.cardNumber}</span>
                            </div>
                            <Badge className={getStatusColor(card.status)}>
                              {card.status.charAt(0).toUpperCase() + card.status.slice(1)}
                            </Badge>
                          </div>
                          
                          <div className="space-y-2 text-sm">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{card.holderName}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Phone className="h-4 w-4 text-muted-foreground" />
                              <span>{card.phone}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Type:</span>
                              <Badge variant="secondary">{card.cardType}</Badge>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Created:</span>
                              <span>{card.createdDate}</span>
                            </div>
                          </div>

                          <div className="flex gap-2 mt-3 pt-3 border-t border-border/50">
                            <Button size="sm" variant="outline" className="flex-1 hover:scale-105 transition-all duration-200">
                              <Eye className="h-3 w-3 mr-1" />
                              View
                            </Button>
                            <Button size="sm" variant="outline" className="flex-1 hover:scale-105 transition-all duration-200">
                              <Download className="h-3 w-3 mr-1" />
                              Export
                            </Button>
                            <Button size="sm" variant="outline" className="flex-1 hover:scale-105 transition-all duration-200">
                              <Printer className="h-3 w-3 mr-1" />
                              Print
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardCreation;
