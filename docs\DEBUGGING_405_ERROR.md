# Debugging 405 Method Not Allowed Error

## 🚨 **Current Issue**
Getting `405 Method Not Allowed` error when trying to POST to `/shifts/shift-entries/`

## 🔍 **Debugging Steps Taken**

### 1. **API Endpoint Verification**
- ✅ Confirmed API documentation shows `POST /shifts/shift-entries` should be available
- ✅ Added trailing slash to URL: `/shifts/shift-entries/`
- ✅ Verified request structure matches API documentation

### 2. **Request Payload Fixes**
- ✅ Added `code` field to both Shift and ShiftEntry requests
- ✅ Shortened generated codes to avoid length issues
- ✅ Ensured all required fields are included

### 3. **Authentication Check**
- ✅ Verified API slice uses correct authentication headers
- ✅ Token is being sent with requests

### 4. **Debug Logging Added**
- ✅ Added console logging for all API calls
- ✅ Added test function to check endpoint accessibility
- ✅ Enhanced error reporting

## 🔧 **Possible Solutions**

### **Solution 1: Backend Endpoint Issue**
The 405 error suggests the backend might not have the POST method enabled for this endpoint.

**Check with backend team:**
- Is `POST /shifts/shift-entries/` properly configured?
- Are there any middleware or routing issues?
- Is the endpoint behind authentication that might be failing?

### **Solution 2: Alternative API Approach**
If the shift-entries endpoint is not working, we could use an alternative approach:

```javascript
// Option A: Use PATCH on existing shift to add entry
PATCH /shifts/{code} 
{
  "current_entry": {
    "start_time": "2024-01-01T09:00:00Z",
    "started_early": false,
    "started_late": false
  }
}

// Option B: Use a different endpoint structure
POST /shifts/{shift_code}/entries/
{
  "start_time": "2024-01-01T09:00:00Z",
  "started_early": false,
  "started_late": false
}
```

### **Solution 3: Direct Shift Management**
Simplify by managing clock in/out directly on the shift:

```javascript
// Clock In: Update shift with actual start time
PATCH /shifts/{code}
{
  "actual_start_time": "2024-01-01T09:00:00Z",
  "started_early": false,
  "started_late": false
}

// Clock Out: Update shift with actual end time  
PATCH /shifts/{code}
{
  "actual_end_time": "2024-01-01T17:00:00Z",
  "ended_early": false,
  "ended_late": false
}
```

## 🧪 **Testing Commands**

### **Test Endpoint Directly**
```bash
# Test GET (should work)
curl -X GET "https://sandbox.gmcplace.co.ke/api/shifts/shift-entries/" \
  -H "Authorization: Token YOUR_TOKEN"

# Test POST (currently failing)
curl -X POST "https://sandbox.gmcplace.co.ke/api/shifts/shift-entries/" \
  -H "Authorization: Token YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "TEST123",
    "shift": "SHIFT_CODE",
    "shift_start_time": "2024-01-01T09:00:00Z",
    "shift_end_time": "2024-01-01T17:00:00Z",
    "start_time": "2024-01-01T09:00:00Z",
    "started_early": false,
    "started_late": false,
    "ended_early": false,
    "ended_late": false
  }'
```

### **Check Available Methods**
```bash
curl -X OPTIONS "https://sandbox.gmcplace.co.ke/api/shifts/shift-entries/" \
  -H "Authorization: Token YOUR_TOKEN"
```

## 📋 **Next Steps**

1. **Verify Backend Configuration**
   - Check if POST method is enabled for `/shifts/shift-entries/`
   - Verify endpoint routing and middleware
   - Check authentication requirements

2. **Test Alternative Endpoints**
   - Try different URL patterns
   - Test with minimal payload
   - Check if endpoint exists at all

3. **Implement Fallback Solution**
   - Use direct shift management approach
   - Implement client-side time tracking
   - Store clock in/out data locally until backend is fixed

## 🔄 **Current Implementation Status**

- ✅ Frontend code is ready and properly structured
- ✅ API calls are correctly formatted
- ✅ Error handling is comprehensive
- ✅ UI is responsive and user-friendly
- ❌ Backend endpoint is not responding to POST requests

The frontend implementation is complete and will work as soon as the backend endpoint issue is resolved.
