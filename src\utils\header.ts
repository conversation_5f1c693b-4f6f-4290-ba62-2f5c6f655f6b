import Cookies from "js-cookie";

// use in default request
export const contentHeader = () => {
  let jwtToken = Cookies.get("orl-tkn");

  return {
    "Content-Type": "application/json",
    Authorization: `Token ${jwtToken}`,
  };
};

// use in multipart form request
export const noContentHeader = () => {
  let jwtToken = Cookies.get("orl-tkn");

  return {
    Authorization: `Token ${jwtToken}`,
  };
};

// use when on none auth requests
export const noAuthHeader = () => {
  return {
    "Content-Type": "application/json",
  };
};
