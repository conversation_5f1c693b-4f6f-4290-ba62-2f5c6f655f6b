import { Outlet } from "react-router-dom";
import AutoLogoutProvider from "./AutoLogoutProvider";

const IdleTimeoutModal = () => {
    return (
        <AutoLogoutProvider
            warningTime={13 * 60} // 13 minutes - show warning 2 minutes before logout
            logoutTime={15 * 60}  // 15 minutes - auto logout after 15 minutes of inactivity
            enabled={true}
        >
            <Outlet />
        </AutoLogoutProvider>
    );
}

export default IdleTimeoutModal;