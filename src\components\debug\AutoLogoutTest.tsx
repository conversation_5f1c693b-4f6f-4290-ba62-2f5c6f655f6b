import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAutoLogout } from '@/hooks/useAutoLogout';
import { useAuthHook } from '@/utils/useAuthHook';
import { Badge } from '@/components/ui/badge';

const AutoLogoutTest: React.FC = () => {
  const { isAuthenticated } = useAuthHook();
  
  // Use shorter times for testing: 10 seconds warning, 15 seconds logout
  const { state, resetTimer, extendSession, forceLogout } = useAutoLogout({
    warningTime: 10, // 10 seconds for testing
    logoutTime: 15,  // 15 seconds for testing
    enabled: isAuthenticated
  });

  if (!isAuthenticated) {
    return (
      <Card className="w-full max-w-md mx-auto mt-4">
        <CardHeader>
          <CardTitle>Auto-Logout Test</CardTitle>
          <CardDescription>Please log in to test auto-logout functionality</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto mt-4">
      <CardHeader>
        <CardTitle>Auto-Logout Test</CardTitle>
        <CardDescription>
          Testing auto-logout with 10s warning, 15s logout
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Status:</span>
            <Badge variant={state.isActive ? "default" : "secondary"}>
              {state.isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Warning Visible:</span>
            <Badge variant={state.isWarningVisible ? "destructive" : "secondary"}>
              {state.isWarningVisible ? "Yes" : "No"}
            </Badge>
          </div>
          
          {state.isWarningVisible && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Time Remaining:</span>
              <Badge variant="destructive">
                {state.timeRemaining}s
              </Badge>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Button 
            onClick={resetTimer} 
            className="w-full"
            variant="outline"
          >
            Reset Timer (Simulate Activity)
          </Button>
          
          <Button 
            onClick={extendSession} 
            className="w-full"
            variant="default"
          >
            Extend Session
          </Button>
          
          <Button 
            onClick={forceLogout} 
            className="w-full"
            variant="destructive"
          >
            Force Logout
          </Button>
        </div>

        <div className="text-xs text-muted-foreground">
          <p>• Warning appears after 10 seconds of inactivity</p>
          <p>• Auto-logout occurs after 15 seconds of inactivity</p>
          <p>• Move mouse or click to reset timer</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default AutoLogoutTest;
