import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  ShoppingCart,
  DollarSign,
  Users,
  Package,
  TrendingUp,
  TrendingDown,
  Clock,
  AlertCircle,
  CheckCircle,
  BarChart3,
  PieChart,
  Calendar,
  Settings,
  Plus,
  Eye,
  ArrowRight,
  CreditCard,
  Receipt,
  BrickWall,
} from "lucide-react";
import { useAuthHook } from "@/utils/useAuthHook";
import useCurrentCurrencyStore from "@/zustand/useCurrentCurrencyStore";
import AutoLogoutTest from "@/components/debug/AutoLogoutTest";

const Home = () => {
  const { user_details } = useAuthHook();
  const currentCurrency = useCurrentCurrencyStore((state) => state.currency);

  // Mock data for demonstration
  const todayStats = {
    sales: 15420.5,
    transactions: 127,
    customers: 89,
    avgTransaction: 121.42,
  };

  const recentTransactions = [
    {
      id: "TXN001",
      customer: "John Doe",
      amount: 245.8,
      time: "2 mins ago",
      status: "completed",
    },
    {
      id: "TXN002",
      customer: "Jane Smith",
      amount: 89.5,
      time: "5 mins ago",
      status: "completed",
    },
    {
      id: "TXN003",
      customer: "Mike Johnson",
      amount: 156.2,
      time: "8 mins ago",
      status: "pending",
    },
    {
      id: "TXN004",
      customer: "Sarah Wilson",
      amount: 78.9,
      time: "12 mins ago",
      status: "completed",
    },
  ];

  const lowStockItems = [
    {
      name: "Premium Coffee Beans",
      stock: 5,
      minStock: 20,
      category: "Beverages",
    },
    {
      name: "Wireless Headphones",
      stock: 2,
      minStock: 10,
      category: "Electronics",
    },
    { name: "Organic Tea Bags", stock: 8, minStock: 25, category: "Beverages" },
  ];

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good Morning";
    if (hour < 17) return "Good Afternoon";
    return "Good Evening";
  };

  return (
    <Screen headerContent>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-primary to-secondary rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">
                {getGreeting()},{" "}
                {user_details?.fullnames?.split(" ")[0] || "User"}!
              </h2>
              <p className="text-white/80 mb-4">
                Welcome to your GMC Point of Sale system. Here's what's
                happening today.
              </p>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>{new Date().toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>
                    {new Date().toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </span>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="w-32 h-32 bg-white/10 rounded-full flex items-center justify-center">
                <ShoppingCart className="w-16 h-16 text-white/80" />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Today's Sales
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-success">
                {todayStats.sales}
              </div>
              <p className="text-xs text-muted-foreground flex items-center mt-1">
                <TrendingUp className="w-3 h-3 mr-1 text-success" />
                +12.5% from yesterday
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Transactions
              </CardTitle>
              <Receipt className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {todayStats.transactions}
              </div>
              <p className="text-xs text-muted-foreground flex items-center mt-1">
                <TrendingUp className="w-3 h-3 mr-1 text-success" />
                +8.2% from yesterday
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{todayStats.customers}</div>
              <p className="text-xs text-muted-foreground flex items-center mt-1">
                <TrendingDown className="w-3 h-3 mr-1 text-destructive" />
                -2.1% from yesterday
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Avg. Transaction
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {todayStats.avgTransaction}
              </div>
              <p className="text-xs text-muted-foreground flex items-center mt-1">
                <TrendingUp className="w-3 h-3 mr-1 text-success" />
                +5.7% from yesterday
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Transactions */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Transactions</CardTitle>
                  <CardDescription>Latest sales activity</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Eye className="w-4 h-4 mr-2" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTransactions.map((transaction) => (
                  <div
                    key={transaction.id}
                    className="flex items-center justify-between p-3 rounded-lg border"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <Receipt className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{transaction.customer}</p>
                        <p className="text-sm text-muted-foreground">
                          {transaction.id} • {transaction.time}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">{transaction.amount}</p>
                      <Badge
                        variant={
                          transaction.status === "completed"
                            ? "default"
                            : "secondary"
                        }
                        className="text-xs"
                      >
                        {transaction.status === "completed" ? (
                          <CheckCircle className="w-3 h-3 mr-1" />
                        ) : (
                          <Clock className="w-3 h-3 mr-1" />
                        )}
                        {transaction.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <Plus className="w-4 h-4 mr-2" />
                New Sale
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Users className="w-4 h-4 mr-2" />
                Add Customer
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Package className="w-4 h-4 mr-2" />
                Manage Inventory
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <BarChart3 className="w-4 h-4 mr-2" />
                View Reports
              </Button>
              <Separator />
              <Button className="w-full justify-start" variant="outline">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Bottom Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Low Stock Alert */}
          <Card className="border-secondary/30 bg-secondary/10">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-secondary" />
                <CardTitle className="text-secondary">
                  Low Stock Alert
                </CardTitle>
              </div>
              <CardDescription className="text-secondary/80">
                Items running low on inventory
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {lowStockItems.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-secondary">
                          {item.name}
                        </p>
                        <p className="text-sm text-secondary/80">
                          {item.category}
                        </p>
                      </div>
                      <Badge
                        variant="outline"
                        className="text-secondary border-secondary/30"
                      >
                        {item.stock} left
                      </Badge>
                    </div>
                    <Progress
                      value={(item.stock / item.minStock) * 100}
                      className="h-2"
                    />
                  </div>
                ))}
              </div>
              <Button className="w-full mt-4" variant="outline">
                <BrickWall className="w-4 h-4 mr-2" />
                Manage Stock
              </Button>
            </CardContent>
          </Card>

          {/* Performance Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Overview</CardTitle>
              <CardDescription>This week vs last week</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Sales Target</span>
                  <span className="text-sm text-muted-foreground">
                    75% achieved
                  </span>
                </div>
                <Progress value={75} className="h-2" />

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    Customer Satisfaction
                  </span>
                  <span className="text-sm text-muted-foreground">92%</span>
                </div>
                <Progress value={92} className="h-2" />

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    Inventory Turnover
                  </span>
                  <span className="text-sm text-muted-foreground">68%</span>
                </div>
                <Progress value={68} className="h-2" />
              </div>

              <div className="mt-6 pt-4 border-t">
                <Button className="w-full" variant="outline">
                  <PieChart className="w-4 h-4 mr-2" />
                  View Detailed Analytics
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Status */}
        <Card className="bg-success/10 border-success/20">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-success rounded-full animate-pulse"></div>
                <div>
                  <p className="font-medium text-success">
                    System Status: Online
                  </p>
                  <p className="text-sm text-success/80">
                    All systems operational
                  </p>
                </div>
              </div>
              <Badge className="bg-success/10 text-success border-success/20">
                <CheckCircle className="w-3 h-3 mr-1" />
                Healthy
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Auto-logout test component - only in development */}
      {import.meta.env.DEV && (
        <div className="mt-8">
          <AutoLogoutTest />
        </div>
      )}
    </Screen>
  );
};

export default Home;
