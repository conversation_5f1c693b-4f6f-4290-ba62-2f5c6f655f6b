"use client"

import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  Sparkles,
  UserRound,
} from "lucide-react"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { useAuthHook } from "@/utils/useAuthHook"
import { DestructiveButton } from "@/components/custom/buttons/buttons"
import { toast } from "@/components/custom/Toast/MyToast"
import { usePostLogoutMutation } from "@/redux/slices/auth"
import { useNavigate } from "react-router-dom"

export function NavUser() {
  const { isMobile } = useSidebar()
  const { isAuthenticated, user_details, token } = useAuthHook();
  const navigate = useNavigate();
  const [postLogout, { isLoading: isLoggingOut }] = usePostLogoutMutation();

  const handleLogout = async () => {
    try {
      if (token?.RefreshToken) {
        await postLogout({ refresh_token: token.RefreshToken }).unwrap();
        toast.success('Logged out successfully');
      } else {
        toast.info('Session ended');
      }
      navigate('/stations-auth');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Logout failed, but session will be cleared');
      navigate('/stations-auth');
    }
  };

  

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size={null}
              className="!py-3 hover:bg-transparent hover:text-foreground bg-gradient-to-b from-primary to-primary"
            >
              <Avatar className="h-11 w-11 rounded-full flex justify-center items-center border  border-primary-foreground">
                <UserRound className='text-primary-foreground' size={29}/>
                {/* <AvatarImage src='' alt={user_details?.fullnames as string} />
                <AvatarFallback className="rounded-lg">{user_details?.fullnames?.charAt(0).toUpperCase()}</AvatarFallback> */}
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight text-primary-foreground">
                <span className="truncate font-semibold">{user_details?.fullnames}</span>
                <span className="truncate text-xs">{user_details?.email}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4 text-primary-foreground" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-60 min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-4 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src='' alt={user_details?.fullnames as string} />
                  <AvatarFallback className="rounded-lg">{user_details?.fullnames?.charAt(0).toUpperCase()}</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{user_details?.fullnames}</span>
                  <span className="truncate text-xs">{user_details?.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem className='hover:!bg-transparent hover:!text-foreground'><Sparkles />My Account</DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            <DropdownMenuGroup>
              <DropdownMenuItem className="truncate max-w-[200px] hover:!bg-transparent hover:!text-foreground">{user_details?.fullnames}</DropdownMenuItem>
              <DropdownMenuItem className="truncate max-w-[200px] hover:!bg-transparent hover:!text-foreground">{user_details?.email}</DropdownMenuItem>
              <DropdownMenuItem className="truncate max-w-[200px] hover:!bg-transparent hover:!text-foreground">{user_details?.department} Dept.</DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            <DropdownMenuItem className=' hover:!bg-transparent'>
              <DestructiveButton
                size="sm"
                className="w-full flex item-center gap-2"
                onClick={handleLogout}
                disabled={isLoggingOut}
              >
                <LogOut size={20} />
                {isLoggingOut ? 'Logging out...' : 'Log out'}
              </DestructiveButton>
            </DropdownMenuItem>

          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
