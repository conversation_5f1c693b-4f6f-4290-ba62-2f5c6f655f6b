import {
  BaseQuery<PERSON><PERSON>,
  create<PERSON><PERSON>,
  <PERSON>tch<PERSON>rgs,
  fetchBaseQuery,
} from "@reduxjs/toolkit/query/react";
import { RootState } from "./store";
import { logout } from "./authSlice";

const environment = import.meta.env.VITE_PROD;

const url =
  environment == "production"
    ? import.meta.env.VITE_API_URL_PROD
    : import.meta.env.VITE_API_URL_DEV;



const baseQueryWithReauth = async (
  args: string | FetchArgs,
  api: BaseQueryApi,
  extraOptions: Record<string, any>
) => {


  const baseQuery = fetchBaseQuery({
    baseUrl: url,
    prepareHeaders: (headers, { getState, endpoint }) => {
      const publicEndpoints = [
        "postLogin",
        "postForgotPassword",
        "postRegistration",
        "healthCheck",
      ];
      const isPublicEndpoint = publicEndpoints.some((ep) =>
        endpoint.includes(ep)
      );

      if (!isPublicEndpoint) {
        const token = (getState() as RootState).auth.token?.access_token;
        if (token) {
          headers.set("Authorization", `Token ${token}`);
        }
      }
      return headers;
    },
  });

  const result = await baseQuery(args, api, extraOptions);

  // 401 logout
  if (result?.error?.status === 401) {
    api.dispatch(logout());
  }

  return result;
};

// Define a service using a base URL and expected endpoints
export const apiSlice = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),

  tagTypes: [
    "Auth",
    "Permissions",
    "Users",
    "Branches",
    "CostCenters",
    "Printers",
    "ReceiptTemplates",
    "RevenueCenters",
    "Stores",
    "SupplierBankDetails",
    "SupplierCategories",
    "Suppliers",
    "TaxClasses",
    "TaxRates",
    "UnitsOfMeasure",
    "UnitOfMeasures",
    "Workstations",
    "UserRoles",
    "StoreRequisitions",
    "StoreRequisitionItems",
    "PurchaseRequisitions",
    "PurchaseRequisitionItems",
    "RFQs",
    "RFQItems",
    "RFQResponses",
    "RFQResponseItems",
    "BidAnalyses",
    "BidAnalysisLines",
    "PurchaseOrders",
    "PurchaseOrderItems",
    "GRNs",
    "GRNItems",
    "Invoices",
    "InvoiceItems",
    "GuestCheck",
    "ComboMenu",
    "ComboComponent",
    "ProductMainCategories",
    "ProductCategories",
    "Menu",
    "MenuGroups",
    "Orders",
    "Recipes",
    "RecipeGroups",
    "Tables",
    "Shifts",
    "ShiftEntries"
  ],
});
