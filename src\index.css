@tailwind base;
@tailwind components;
@tailwind utilities;

/* -----------------------------------
  Light Theme 
----------------------------------- */
:root {
  color-scheme: light;

  font-family: "Manrope", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
  font-optical-sizing: auto;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Light theme tokens - Red, Yellow, Black */
  --background: 0 0% 98%;
  --foreground: 0 0% 10%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 10%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 10%;
  --primary: 0 100% 50%; /* Red */
  --primary-foreground: 0 0% 100%;
  --secondary: 60 100% 50%; /* Yellow */
  --secondary-foreground: 0 0% 10%;
  --muted: 0 0% 96%;
  --muted-foreground: 0 0% 45%;
  --accent: 0 0% 0%; /* Black accent */
  --accent-foreground: 0 0% 98%;

  --success: 120 61% 34%; /* Green for success */
  --success-foreground: 0 0% 100%;

  --destructive: 0 100% 50%; /* Red for destructive actions */
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 90%;
  --input: 0 0% 100%;
  --ring: 0 100% 50%; /* Red ring */
  --chart-1: 0 100% 50%; /* Red */
  --chart-2: 60 100% 50%; /* Yellow */
  --chart-3: 0 0% 0%; /* Black */
  --chart-4: 0 100% 40%; /* Darker Red */
  --chart-5: 60 100% 40%; /* Darker Yellow */
  --radius: 0.5rem;

  /* Sidebar tokens for Light */
  --sidebar-background: 0 0% 100%;
  --sidebar-foreground: 0 0% 10%;
  --sidebar-primary: 0 100% 50%; /* Red */
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 60 100% 50%; /* Yellow */
  --sidebar-accent-foreground: 0 0% 10%;
  --sidebar-border: 0 0% 90%;
  --sidebar-ring: 0 100% 50%; /* Red */
}

/* -----------------------------------
   Dark Theme 
----------------------------------- */
.dark {
  color-scheme: dark;

  --background: 0 0% 8%;
  --foreground: 0 0% 95%;
  --card: 0 0% 12%;
  --card-foreground: 0 0% 95%;
  --popover: 0 0% 8%;
  --popover-foreground: 0 0% 95%;
  --primary: 0 100% 60%; /* Red - slightly lighter for dark mode */
  --primary-foreground: 0 0% 100%;
  --secondary: 60 100% 60%; /* Yellow - slightly lighter for dark mode */
  --secondary-foreground: 0 0% 10%;
  --muted: 0 0% 15%;
  --muted-foreground: 0 0% 70%;
  --accent: 0 0% 0%; /* Black accent for dark mode */
  --accent-foreground: 0 0% 10%;

  /*
    UPDATED success color in dark theme:
    Increase lightness so it’s clearly visible against a dark background
  */
  --success: 142.4 71.8% 45%;
  --success-foreground: 0 0% 100%;

  --destructive: 0 100% 60%; /* Red for destructive actions */
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 20%;
  --input: 0 0% 15%;
  --ring: 0 100% 60%; /* Red ring */
  --chart-1: 0 100% 60%; /* Red */
  --chart-2: 60 100% 60%; /* Yellow */
  --chart-3: 0 0% 0%; /* Black */
  --chart-4: 0 100% 50%; /* Darker Red */
  --chart-5: 60 100% 50%; /* Darker Yellow */
  --radius: 0.5rem;

  /* Sidebar tokens for Dark */
  --sidebar-background: 0 0% 10%;
  --sidebar-foreground: 0 0% 95%;
  --sidebar-primary: 0 100% 60%; /* Red */
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 60 100% 60%; /* Yellow */
  --sidebar-accent-foreground: 0 0% 10%;
  --sidebar-border: 0 0% 20%;
  --sidebar-ring: 0 100% 60%; /* Red */
}

body {
  margin: 0;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-[hsl(var(--background))] text-[hsl(var(--foreground))];
  }
}

/* Custom scrollbar styles for better mobile experience */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-300 {
    scrollbar-color: #d1d5db transparent;
  }

  .scrollbar-track-gray-100 {
    scrollbar-color: #d1d5db #f3f4f6;
  }

  /* Webkit scrollbar styles for better cross-browser support */
  .scrollbar-thin::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Dark theme scrollbar */
  .dark .scrollbar-thin::-webkit-scrollbar-track {
    background: #374151;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #6b7280;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Enhanced mobile touch scrolling */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Table specific scrolling optimizations for all screen sizes */
  .table-mobile-scroll {
    overflow-x: auto;
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
    width: 100%;
    max-width: 100%;
  }

  .table-mobile-scroll::-webkit-scrollbar {
    height: 12px;
  }

  .table-mobile-scroll::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 6px;
  }

  .table-mobile-scroll::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 6px;
    border: 2px solid #f3f4f6;
  }

  .table-mobile-scroll::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Dark theme for table scroll */
  .dark .table-mobile-scroll {
    scrollbar-color: #6b7280 #374151;
  }

  .dark .table-mobile-scroll::-webkit-scrollbar-track {
    background: #374151;
  }

  .dark .table-mobile-scroll::-webkit-scrollbar-thumb {
    background: #6b7280;
    border: 2px solid #374151;
  }

  .dark .table-mobile-scroll::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Ensure tables don't overflow on larger screens */
  @media (min-width: 768px) {
    .table-mobile-scroll {
      max-width: 100%;
      overflow-x: auto;
    }

    .table-mobile-scroll table {
      width: 100%;
      table-layout: auto;
    }
  }

  @media (min-width: 1024px) {
    .table-mobile-scroll {
      max-width: calc(100vw - 2rem);
    }
  }

  @media (min-width: 1280px) {
    .table-mobile-scroll {
      max-width: calc(100vw - 4rem);
    }
  }
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Enhanced Mobile and Tablet Responsiveness */
@media (max-width: 1279px) {
  .mobile-order-panel {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  .mobile-order-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .mobile-order-scroll {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary)) transparent;
  }

  .mobile-order-footer {
    flex-shrink: 0;
    padding-bottom: env(safe-area-inset-bottom, 1rem);
  }
}

/* Tablet specific improvements */
@media (min-width: 768px) and (max-width: 1279px) {
  .tablet-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .tablet-spacing {
    padding: 1.5rem;
    gap: 1.5rem;
  }
}

/* Mobile specific improvements */
@media (max-width: 767px) {
  .mobile-compact {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .mobile-text-scale .text-xs {
    font-size: 0.75rem;
  }

  .mobile-text-scale .text-sm {
    font-size: 0.875rem;
  }

  .mobile-button-compact {
    height: 2.5rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}

/* Very small screens */
@media (max-width: 375px) {
  .mobile-compact {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .mobile-text-scale .text-xs {
    font-size: 0.6875rem;
  }

  .mobile-button-compact {
    height: 2.25rem;
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* Enhanced animations and transitions */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.4s ease-out;
}

/* Custom scrollbar for webkit browsers */
.mobile-order-scroll::-webkit-scrollbar {
  width: 6px;
}

.mobile-order-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-order-scroll::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.3);
  border-radius: 3px;
}

.mobile-order-scroll::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.5);
}

/* Safe area support for devices with notches */
.pb-safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom, 1rem);
}
