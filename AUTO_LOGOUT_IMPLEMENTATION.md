# Auto-Logout Implementation

## Overview

I have successfully implemented a comprehensive auto-logout system for the GMC Workstation application that automatically logs out users after 15 minutes of inactivity, with a warning modal appearing 2 minutes before logout (at 13 minutes of inactivity).

## Features Implemented

### 1. **Auto-Logout Hook** (`src/hooks/useAutoLogout.ts`)
- Manages idle detection and automatic logout functionality
- Configurable warning and logout times
- Integrates with Redux for proper session management
- Handles API logout calls and local state cleanup
- Provides manual control functions (reset, extend, force logout)

### 2. **Warning Modal Component** (`src/components/auth/AutoLogoutWarningModal.tsx`)
- Professional modal that appears 2 minutes before logout
- Real-time countdown timer showing remaining time
- Visual urgency indicators (colors change as time decreases)
- "Keep Me Logged In" button to extend session
- Optional "Logout Now" button for immediate logout
- Responsive design with accessibility features

### 3. **Auto-Logout Provider** (`src/components/auth/AutoLogoutProvider.tsx`)
- Wrapper component that provides auto-logout functionality
- Integrates the hook with the modal component
- Only activates for authenticated users
- Configurable timing parameters

### 4. **Enhanced Idle Timeout Utility** (`src/utils/userIdleTimeout.ts`)
- Enhanced the existing idle timeout hook
- Added manual timer reset functionality
- Better integration with React lifecycle

## Configuration

The auto-logout system is configured with the following default settings:

- **Warning Time**: 13 minutes (780 seconds)
- **Logout Time**: 15 minutes (900 seconds)
- **Warning Duration**: 2 minutes (120 seconds)

These can be customized when using the `AutoLogoutProvider`:

```tsx
<AutoLogoutProvider
  warningTime={13 * 60} // 13 minutes
  logoutTime={15 * 60}  // 15 minutes
  enabled={true}
>
  {children}
</AutoLogoutProvider>
```

## Integration

The auto-logout system has been integrated into the main application:

1. **App.tsx**: The `AutoLogoutProvider` wraps the entire application
2. **Authentication**: Only activates when user is authenticated
3. **Session Management**: Properly clears Redux state and calls logout API
4. **Navigation**: Redirects to login page after logout

## User Experience

### Normal Flow
1. User logs in and uses the application normally
2. After 13 minutes of inactivity, a warning modal appears
3. Modal shows a 2-minute countdown with "Keep Me Logged In" button
4. If user clicks the button, session is extended and timer resets
5. If no action is taken, user is automatically logged out after 15 minutes total

### Activity Detection
The system detects the following user activities to reset the timer:
- Mouse movement
- Keyboard input
- Mouse clicks
- Touch events (for mobile)
- Scrolling

### Visual Feedback
- Warning modal changes color as time decreases (yellow → orange → red)
- Real-time countdown display
- Progress bar showing time elapsed
- Clear call-to-action buttons

## Security Features

1. **API Integration**: Calls the logout API endpoint to invalidate server-side sessions
2. **State Cleanup**: Clears all Redux state and persisted data
3. **Error Handling**: Continues with local logout even if API call fails
4. **Token Management**: Properly handles both access and refresh tokens

## Testing

### Test Page
A dedicated test page has been created at `/auto-logout-test` with:
- Real-time status display
- Manual control buttons
- Shortened timers for quick testing (5s warning, 10s logout)
- Step-by-step instructions

### Test Components
- **AutoLogoutTest**: Debug component for the Home page (development only)
- **AutoLogoutTestPage**: Full test page with comprehensive testing interface

## Edge Cases Handled

1. **Manual Logout**: Warning modal disappears if user logs out manually
2. **Authentication Changes**: System deactivates when user becomes unauthenticated
3. **Component Unmounting**: Proper cleanup of timers and event listeners
4. **Multiple Tabs**: Each tab manages its own timer independently
5. **API Failures**: Graceful fallback to local logout if server is unavailable

## Files Created/Modified

### New Files
- `src/hooks/useAutoLogout.ts` - Main auto-logout hook
- `src/components/auth/AutoLogoutProvider.tsx` - Provider component
- `src/components/auth/AutoLogoutWarningModal.tsx` - Warning modal
- `src/components/debug/AutoLogoutTest.tsx` - Debug component
- `src/pages/AutoLogoutTestPage.tsx` - Test page

### Modified Files
- `src/App.tsx` - Added AutoLogoutProvider and test route
- `src/components/auth/IdleTimeoutModal.tsx` - Replaced with new system
- `src/utils/userIdleTimeout.ts` - Enhanced with manual controls
- `src/pages/Home.tsx` - Added debug component (development only)

## Usage Instructions

### For Users
1. Log in to the application
2. Use the application normally
3. If inactive for 13 minutes, a warning will appear
4. Click "Keep Me Logged In" to continue your session
5. If no action is taken, you'll be logged out after 15 minutes

### For Developers
1. The system is automatically active for all authenticated users
2. Use the test page at `/auto-logout-test` for development testing
3. Customize timing by modifying the AutoLogoutProvider props
4. Monitor the debug component on the Home page during development

## Future Enhancements

1. **Server-Side Coordination**: Sync idle detection across multiple tabs
2. **Configurable Settings**: Allow users to customize timeout periods
3. **Activity Logging**: Track user activity patterns for analytics
4. **Grace Period**: Allow brief reconnection after logout
5. **Progressive Warnings**: Multiple warning stages (e.g., 5 min, 2 min, 30 sec)

The implementation is production-ready and follows React best practices with proper error handling, cleanup, and user experience considerations.
