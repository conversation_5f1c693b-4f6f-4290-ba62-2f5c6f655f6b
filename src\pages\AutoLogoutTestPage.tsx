import React from 'react';
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAutoLogout } from '@/hooks/useAutoLogout';
import { useAuthHook } from '@/utils/useAuthHook';
import { AlertTriangle, Clock, Shield, User } from 'lucide-react';

const AutoLogoutTestPage: React.FC = () => {
  const { isAuthenticated, user_details } = useAuthHook();
  
  // Use very short times for testing: 5 seconds warning, 10 seconds logout
  const { state, resetTimer, extendSession, forceLogout } = useAutoLogout({
    warningTime: 5,  // 5 seconds for testing
    logoutTime: 10,  // 10 seconds for testing
    enabled: isAuthenticated
  });

  return (
    <Screen headerContent={<h1 className="text-xl font-semibold">Auto-Logout Test</h1>}>
      <div className="max-w-4xl mx-auto space-y-6">
        
        {/* Authentication Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Authentication Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="font-medium">Status:</span>
                <Badge variant={isAuthenticated ? "default" : "secondary"}>
                  {isAuthenticated ? "Authenticated" : "Not Authenticated"}
                </Badge>
              </div>
              
              {isAuthenticated && user_details && (
                <div className="flex items-center justify-between">
                  <span className="font-medium">User:</span>
                  <span className="text-sm">{user_details.fullnames || user_details.email}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Auto-Logout Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Auto-Logout Status
            </CardTitle>
            <CardDescription>
              Testing with 5 second warning, 10 second logout
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-sm text-muted-foreground mb-1">Timer Active</div>
                  <Badge variant={state.isActive ? "default" : "secondary"} className="text-lg px-3 py-1">
                    {state.isActive ? "Yes" : "No"}
                  </Badge>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-sm text-muted-foreground mb-1">Warning Visible</div>
                  <Badge variant={state.isWarningVisible ? "destructive" : "secondary"} className="text-lg px-3 py-1">
                    {state.isWarningVisible ? "Yes" : "No"}
                  </Badge>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-sm text-muted-foreground mb-1">Time Remaining</div>
                  <Badge 
                    variant={state.timeRemaining > 0 ? "destructive" : "secondary"} 
                    className="text-lg px-3 py-1"
                  >
                    {state.timeRemaining > 0 ? `${state.timeRemaining}s` : "N/A"}
                  </Badge>
                </div>
              </div>

              {/* Control Buttons */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <Button 
                  onClick={resetTimer} 
                  variant="outline"
                  className="w-full"
                  disabled={!isAuthenticated}
                >
                  <Clock className="h-4 w-4 mr-2" />
                  Reset Timer
                </Button>
                
                <Button 
                  onClick={extendSession} 
                  variant="default"
                  className="w-full"
                  disabled={!isAuthenticated}
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Extend Session
                </Button>
                
                <Button 
                  onClick={forceLogout} 
                  variant="destructive"
                  className="w-full"
                  disabled={!isAuthenticated}
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Force Logout
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Test Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="flex items-start gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-bold">1</div>
                <div>
                  <strong>Login:</strong> Make sure you're logged in to test the auto-logout functionality.
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-bold">2</div>
                <div>
                  <strong>Wait:</strong> Stop moving your mouse and keyboard for 5 seconds to see the warning modal.
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-bold">3</div>
                <div>
                  <strong>Test Modal:</strong> When the warning appears, you can click "Keep Me Logged In" to extend the session.
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-bold">4</div>
                <div>
                  <strong>Auto-Logout:</strong> If you don't interact with the modal, you'll be logged out after 10 seconds total.
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-bold">5</div>
                <div>
                  <strong>Activity Reset:</strong> Any mouse movement, keyboard input, or clicking will reset the timer.
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {!isAuthenticated && (
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="font-medium text-orange-800">Authentication Required</p>
                  <p className="text-sm text-orange-700">
                    Please log in to test the auto-logout functionality. Navigate to the login page and authenticate first.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </Screen>
  );
};

export default AutoLogoutTestPage;
