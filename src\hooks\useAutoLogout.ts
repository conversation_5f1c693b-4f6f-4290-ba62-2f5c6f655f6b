import { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { logout } from '@/redux/authSlice';
import { usePostLogoutMutation } from '@/redux/slices/auth';
import { useAuthHook } from '@/utils/useAuthHook';
import { toast } from '@/components/custom/Toast/MyToast';

interface AutoLogoutConfig {
  warningTime: number; // Time in seconds before logout to show warning (default: 13 minutes)
  logoutTime: number; // Time in seconds before auto logout (default: 15 minutes)
  enabled?: boolean; // Whether auto logout is enabled (default: true)
  events?: string[]; // DOM events that reset the idle timer
}

interface AutoLogoutState {
  isWarningVisible: boolean;
  timeRemaining: number; // Time remaining in seconds until logout
  isActive: boolean; // Whether the auto logout is currently active
}

interface AutoLogoutReturn {
  state: AutoLogoutState;
  resetTimer: () => void;
  extendSession: () => void;
  forceLogout: () => void;
}

const DEFAULT_CONFIG: Required<AutoLogoutConfig> = {
  warningTime: 13 * 60, // 13 minutes in seconds
  logoutTime: 15 * 60, // 15 minutes in seconds
  enabled: true,
  events: ['mousemove', 'keydown', 'mousedown', 'touchstart', 'scroll', 'click']
};

export const useAutoLogout = (config: Partial<AutoLogoutConfig> = {}): AutoLogoutReturn => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const { isAuthenticated, token } = useAuthHook();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [postLogout] = usePostLogoutMutation();

  const [state, setState] = useState<AutoLogoutState>({
    isWarningVisible: false,
    timeRemaining: 0,
    isActive: false
  });

  const warningTimerRef = useRef<NodeJS.Timeout | null>(null);
  const logoutTimerRef = useRef<NodeJS.Timeout | null>(null);
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());
  const eventListenersRef = useRef<(() => void)[]>([]);

  // Clear all timers
  const clearAllTimers = useCallback(() => {
    if (warningTimerRef.current) {
      clearTimeout(warningTimerRef.current);
      warningTimerRef.current = null;
    }
    if (logoutTimerRef.current) {
      clearTimeout(logoutTimerRef.current);
      logoutTimerRef.current = null;
    }
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }
  }, []);

  // Handle user activity
  const handleActivity = useCallback(() => {
    lastActivityRef.current = Date.now();

    // If warning is visible, hide it and reset timers
    if (state.isWarningVisible) {
      setState(prev => ({
        ...prev,
        isWarningVisible: false,
        timeRemaining: 0
      }));
      clearAllTimers();
    }
  }, [state.isWarningVisible, clearAllTimers]);

  // Perform logout
  const performLogout = useCallback(async () => {
    try {
      clearAllTimers();
      setState(prev => ({
        ...prev,
        isWarningVisible: false,
        timeRemaining: 0,
        isActive: false
      }));

      // Call logout API if we have a refresh token
      if (token?.refresh_token) {
        try {
          await postLogout({ refresh_token: token.refresh_token }).unwrap();
        } catch (error) {
          console.error('Logout API call failed:', error);
          // Continue with local logout even if API fails
        }
      }

      // Clear Redux state
      dispatch(logout());
      
      // Show notification
      toast.info('You have been logged out due to inactivity');
      
      // Navigate to login
      navigate('/stations-auth', { replace: true });
    } catch (error) {
      console.error('Error during auto logout:', error);
      // Force logout even if there's an error
      dispatch(logout());
      navigate('/stations-auth', { replace: true });
    }
  }, [token, postLogout, dispatch, navigate, clearAllTimers]);

  // Start countdown when warning is shown
  const startCountdown = useCallback(() => {
    const warningDuration = (finalConfig.logoutTime - finalConfig.warningTime) * 1000; // Convert to milliseconds
    let timeLeft = Math.floor(warningDuration / 1000); // Convert to seconds

    setState(prev => ({
      ...prev,
      isWarningVisible: true,
      timeRemaining: timeLeft
    }));

    countdownTimerRef.current = setInterval(() => {
      timeLeft -= 1;
      setState(prev => ({
        ...prev,
        timeRemaining: timeLeft
      }));

      if (timeLeft <= 0) {
        if (countdownTimerRef.current) {
          clearInterval(countdownTimerRef.current);
          countdownTimerRef.current = null;
        }
      }
    }, 1000);
  }, [finalConfig.logoutTime, finalConfig.warningTime]);

  // Start the warning and logout timers
  const startTimers = useCallback(() => {
    clearAllTimers();

    // Set warning timer
    warningTimerRef.current = setTimeout(() => {
      startCountdown();
    }, finalConfig.warningTime * 1000);

    // Set logout timer
    logoutTimerRef.current = setTimeout(() => {
      performLogout();
    }, finalConfig.logoutTime * 1000);

    setState(prev => ({
      ...prev,
      isActive: true
    }));
  }, [finalConfig.warningTime, finalConfig.logoutTime, startCountdown, performLogout, clearAllTimers]);

  // Reset timer function
  const resetTimer = useCallback(() => {
    if (finalConfig.enabled && isAuthenticated) {
      handleActivity();
    }
  }, [finalConfig.enabled, isAuthenticated, handleActivity]);

  // Extend session (called when user clicks "Keep Me Logged In")
  const extendSession = useCallback(() => {
    handleActivity();
    clearAllTimers();
    if (finalConfig.enabled && isAuthenticated) {
      startTimers();
    }
    toast.success('Session extended successfully');
  }, [handleActivity, clearAllTimers, finalConfig.enabled, isAuthenticated, startTimers]);

  // Force logout function
  const forceLogout = useCallback(() => {
    performLogout();
  }, [performLogout]);

  // Set up event listeners for user activity
  useEffect(() => {
    if (!finalConfig.enabled || !isAuthenticated) {
      return;
    }

    // Remove existing event listeners
    eventListenersRef.current.forEach(removeListener => removeListener());
    eventListenersRef.current = [];

    // Add new event listeners
    finalConfig.events.forEach(eventType => {
      const listener = () => {
        handleActivity();
        // Restart timers if not in warning state
        if (!state.isWarningVisible && finalConfig.enabled && isAuthenticated) {
          clearAllTimers();
          startTimers();
        }
      };
      document.addEventListener(eventType, listener, { passive: true });

      // Store cleanup function
      eventListenersRef.current.push(() => {
        document.removeEventListener(eventType, listener);
      });
    });

    return () => {
      // Cleanup event listeners
      eventListenersRef.current.forEach(removeListener => removeListener());
      eventListenersRef.current = [];
    };
  }, [finalConfig.enabled, finalConfig.events, isAuthenticated, handleActivity, state.isWarningVisible, clearAllTimers, startTimers]);

  // Initialize timers when component mounts or authentication state changes
  useEffect(() => {
    if (finalConfig.enabled && isAuthenticated) {
      startTimers();
    } else {
      clearAllTimers();
      setState({
        isWarningVisible: false,
        timeRemaining: 0,
        isActive: false
      });
    }

    return () => {
      clearAllTimers();
    };
  }, [finalConfig.enabled, isAuthenticated, startTimers, clearAllTimers]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearAllTimers();
      // Cleanup event listeners
      eventListenersRef.current.forEach(removeListener => removeListener());
      eventListenersRef.current = [];
    };
  }, [clearAllTimers]);

  // Handle authentication state changes - clear warning if user logs out
  useEffect(() => {
    if (!isAuthenticated && state.isWarningVisible) {
      setState(prev => ({
        ...prev,
        isWarningVisible: false,
        timeRemaining: 0,
        isActive: false
      }));
      clearAllTimers();
    }
  }, [isAuthenticated, state.isWarningVisible, clearAllTimers]);

  return {
    state,
    resetTimer,
    extendSession,
    forceLogout
  };
};
