import { useState } from "react";
import Logo from "@/assets/logo.png";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Delete, ArrowLeft } from "lucide-react";
import { usePinLoginMutation } from "@/redux/slices/auth";

const StationPinLogin = () => {
  const navigate = useNavigate();
  const [pin, setPin] = useState("");
  const maxLength = 4;

  const [pinLogin, { isLoading: isLoggingIn }] = usePinLoginMutation();

  const handleNumberClick = (value: string) => {
    if (pin.length < maxLength) {
      setPin((prev) => prev + value);
    }
  };

  const handleClear = () => {
    setPin("");
  };

  const handleBack = () => {
    setPin((prev) => prev.slice(0, -1));
  };

  const handleEnter = async () => {
    if (pin.length !== maxLength) {
      toast.error("Please enter your 4 digit pin!!");
      return;
    }

    try {
      const res = await pinLogin({ pin }).unwrap();
      if (res.employee_no) {
        toast.success("Login successful");
        navigate("/stations/home");
      }
    } catch (error) {
      toast.error("Invalid pin");
      console.log("Login Error", error);
    }

    // navigate("/stations/home");
    console.log("PIN submitted:", pin);
  };

  return (
    <div className="max-h-[screen] flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md shadow-2xl border-border/50 backdrop-blur-sm ">
        <CardHeader className="text-center space-y-4 pb-6">
          <div className="flex justify-center">
            <div className="relative">
              <img
                src={Logo}
                alt="Logo"
                className="h-20 w-auto drop-shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/10 to-transparent rounded-full blur-xl" />
            </div>
          </div>
          <div className="space-y-2">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Enter PIN
            </h1>
            <p className="text-muted-foreground text-sm">
              Please enter your 4-digit PIN to continue
            </p>
          </div>
        </CardHeader>

        <CardContent className="space-y-8">
          <div className="flex justify-center gap-3">
            {[...Array(maxLength)].map((_, index) => (
              <div
                key={index}
                className={`
                  w-14 h-14 rounded-xl border-2 flex items-center justify-center text-2xl font-bold
                  transition-all duration-300 ease-in-out
                  ${
                    pin[index]
                      ? "border-primary bg-primary/10 text-primary scale-105 shadow-lg shadow-primary/25"
                      : "border-border bg-card hover:border-primary/50"
                  }
                `}
              >
                {pin[index] ? "●" : ""}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-3 gap-3">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
              <Button
                key={num}
                variant="outline"
                size="lg"
                onClick={() => handleNumberClick(num.toString())}
                className="h-12 text-xl font-semibold hover:scale-105 transition-all duration-200 hover:shadow-lg border-border/50 hover:border-primary/50"
              >
                {num}
              </Button>
            ))}

            <Button
              variant="outline"
              size="lg"
              onClick={() => handleNumberClick("00")}
              className="h-16 text-xl font-semibold hover:scale-105 transition-all duration-200 hover:shadow-lg border-border/50 hover:border-primary/50"
            >
              00
            </Button>

            <Button
              variant="outline"
              size="lg"
              onClick={() => handleNumberClick("0")}
              className="h-16 text-xl font-semibold hover:scale-105 transition-all duration-200 hover:shadow-lg border-border/50 hover:border-primary/50"
            >
              0
            </Button>

            <Button
              variant="outline"
              size="lg"
              onClick={handleBack}
              className="h-16 hover:scale-105 transition-all duration-200 hover:shadow-lg border-border/50 hover:border-destructive/50 hover:text-destructive"
            >
              <Delete className="h-5 w-5" />
            </Button>
          </div>

          <div className="grid grid-cols-3 gap-3 pt-4">
            <Button
              variant="destructive"
              size="lg"
              onClick={handleClear}
              className="h-14 font-semibold hover:scale-105 transition-all duration-200 shadow-lg"
            >
              Clear
            </Button>

            <Button
              size="lg"
              onClick={handleEnter}
              className="h-14 font-semibold hover:scale-105 transition-all duration-200 shadow-lg bg-gradient-to-r from-green-700 to-green-700/90 hover:from-green-700/90 hover:to-green-700"
            >
              Enter
            </Button>

            <Button
              variant="secondary"
              size="lg"
              asChild
              className="h-14 font-semibold hover:scale-105 transition-all duration-200 shadow-lg"
            >
              <Link
                to="/stations"
                className="flex items-center justify-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Cancel
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StationPinLogin;